"""
OCR engine for extracting text from number plates.
"""

import cv2
import numpy as np
import re
from typing import List, Tuple, Optional, Dict, Any
from abc import ABC, abstractmethod

try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False

from ..utils.logger import Logger


class OCREngine(ABC):
    """Abstract base class for OCR engines."""
    
    @abstractmethod
    def extract_text(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Extract text from image."""
        pass


class EasyOCREngine(OCREngine):
    """EasyOCR-based text extraction."""
    
    def __init__(self, languages: List[str] = ['en'], 
                 confidence_threshold: float = 0.6,
                 logger: Optional[Logger] = None):
        """
        Initialize EasyOCR engine.
        
        Args:
            languages: List of languages for OCR
            confidence_threshold: Minimum confidence for text detection
            logger: Logger instance
        """
        if not EASYOCR_AVAILABLE:
            raise ImportError("EasyOCR not available. Install with: pip install easyocr")
        
        self.languages = languages
        self.confidence_threshold = confidence_threshold
        self.logger = logger or Logger()
        
        try:
            self.reader = easyocr.Reader(languages, gpu=False)  # Set gpu=True if CUDA available
            self.logger.info(f"EasyOCR initialized with languages: {languages}")
        except Exception as e:
            self.logger.error(f"Failed to initialize EasyOCR: {e}")
            raise
    
    def extract_text(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        Extract text from image using EasyOCR.
        
        Args:
            image: Input image
            
        Returns:
            List of text detection results
        """
        try:
            results = self.reader.readtext(image)
            
            detections = []
            for (bbox, text, confidence) in results:
                if confidence >= self.confidence_threshold:
                    # Convert bbox to standard format
                    x_coords = [point[0] for point in bbox]
                    y_coords = [point[1] for point in bbox]
                    x1, y1 = int(min(x_coords)), int(min(y_coords))
                    x2, y2 = int(max(x_coords)), int(max(y_coords))
                    
                    detection = {
                        'text': text.strip(),
                        'confidence': confidence,
                        'bbox': [x1, y1, x2, y2]
                    }
                    detections.append(detection)
            
            return detections
            
        except Exception as e:
            self.logger.error(f"EasyOCR text extraction failed: {e}")
            return []


class TesseractEngine(OCREngine):
    """Tesseract-based text extraction."""
    
    def __init__(self, config: str = '--oem 3 --psm 8',
                 confidence_threshold: float = 0.6,
                 logger: Optional[Logger] = None):
        """
        Initialize Tesseract engine.
        
        Args:
            config: Tesseract configuration string
            confidence_threshold: Minimum confidence for text detection
            logger: Logger instance
        """
        if not TESSERACT_AVAILABLE:
            raise ImportError("Tesseract not available. Install with: pip install pytesseract")
        
        self.config = config
        self.confidence_threshold = confidence_threshold
        self.logger = logger or Logger()
        
        try:
            # Test if tesseract is available
            pytesseract.get_tesseract_version()
            self.logger.info("Tesseract OCR initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize Tesseract: {e}")
            raise
    
    def extract_text(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        Extract text from image using Tesseract.
        
        Args:
            image: Input image
            
        Returns:
            List of text detection results
        """
        try:
            # Get detailed data from Tesseract
            data = pytesseract.image_to_data(image, config=self.config, output_type=pytesseract.Output.DICT)
            
            detections = []
            n_boxes = len(data['level'])
            
            for i in range(n_boxes):
                confidence = int(data['conf'][i])
                text = data['text'][i].strip()
                
                if confidence >= (self.confidence_threshold * 100) and text:
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                    
                    detection = {
                        'text': text,
                        'confidence': confidence / 100.0,  # Convert to 0-1 range
                        'bbox': [x, y, x + w, y + h]
                    }
                    detections.append(detection)
            
            return detections
            
        except Exception as e:
            self.logger.error(f"Tesseract text extraction failed: {e}")
            return []


class NumberPlateOCR:
    """Main OCR class for number plate text extraction."""
    
    def __init__(self, engine: str = "easyocr", 
                 languages: List[str] = ['en'],
                 confidence_threshold: float = 0.6,
                 logger: Optional[Logger] = None):
        """
        Initialize number plate OCR.
        
        Args:
            engine: OCR engine to use ('easyocr' or 'tesseract')
            languages: List of languages for OCR
            confidence_threshold: Minimum confidence threshold
            logger: Logger instance
        """
        self.engine_name = engine
        self.languages = languages
        self.confidence_threshold = confidence_threshold
        self.logger = logger or Logger()
        
        # Initialize OCR engine
        if engine.lower() == "easyocr":
            self.engine = EasyOCREngine(languages, confidence_threshold, logger)
        elif engine.lower() == "tesseract":
            self.engine = TesseractEngine(confidence_threshold=confidence_threshold, logger=logger)
        else:
            raise ValueError(f"Unsupported OCR engine: {engine}")
        
        self.logger.info(f"NumberPlateOCR initialized with {engine} engine")
    
    def extract_plate_text(self, image: np.ndarray) -> str:
        """
        Extract and clean number plate text.
        
        Args:
            image: Preprocessed plate image
            
        Returns:
            Cleaned plate text
        """
        try:
            # Extract text using OCR engine
            detections = self.engine.extract_text(image)
            
            if not detections:
                return ""
            
            # Combine all detected text
            all_text = " ".join([det['text'] for det in detections])
            
            # Clean and format the text
            cleaned_text = self._clean_plate_text(all_text)
            
            self.logger.debug(f"Extracted plate text: '{cleaned_text}'")
            return cleaned_text
            
        except Exception as e:
            self.logger.error(f"Failed to extract plate text: {e}")
            return ""
    
    def _clean_plate_text(self, text: str) -> str:
        """
        Clean and format extracted text for Indian number plates.
        
        Args:
            text: Raw extracted text
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Remove extra spaces and convert to uppercase
        text = re.sub(r'\s+', '', text.upper())
        
        # Remove special characters except alphanumeric
        text = re.sub(r'[^A-Z0-9]', '', text)
        
        # Indian number plate patterns
        # Format: XX##XX#### or XX##X#### or XX###X###
        patterns = [
            r'^([A-Z]{2})(\d{2})([A-Z]{2})(\d{4})$',  # XX##XX####
            r'^([A-Z]{2})(\d{2})([A-Z]{1})(\d{4})$',  # XX##X####
            r'^([A-Z]{2})(\d{3})([A-Z]{1})(\d{3})$',  # XX###X###
        ]
        
        for pattern in patterns:
            match = re.match(pattern, text)
            if match:
                # Format with proper spacing
                groups = match.groups()
                if len(groups) == 4:
                    return f"{groups[0]}{groups[1]}{groups[2]}{groups[3]}"
        
        # If no pattern matches, return cleaned text as is
        return text
    
    def validate_indian_plate(self, plate_text: str) -> bool:
        """
        Validate if text matches Indian number plate format.
        
        Args:
            plate_text: Plate text to validate
            
        Returns:
            True if valid Indian plate format
        """
        if not plate_text or len(plate_text) < 8:
            return False
        
        # Indian number plate patterns
        patterns = [
            r'^[A-Z]{2}\d{2}[A-Z]{2}\d{4}$',  # XX##XX####
            r'^[A-Z]{2}\d{2}[A-Z]{1}\d{4}$',  # XX##X####
            r'^[A-Z]{2}\d{3}[A-Z]{1}\d{3}$',  # XX###X###
        ]
        
        for pattern in patterns:
            if re.match(pattern, plate_text):
                return True
        
        return False
    
    def get_confidence_score(self, image: np.ndarray) -> float:
        """
        Get overall confidence score for text extraction.
        
        Args:
            image: Input image
            
        Returns:
            Average confidence score
        """
        try:
            detections = self.engine.extract_text(image)
            if not detections:
                return 0.0
            
            confidences = [det['confidence'] for det in detections]
            return sum(confidences) / len(confidences)
            
        except Exception as e:
            self.logger.error(f"Failed to get confidence score: {e}")
            return 0.0
    
    def preprocess_for_ocr(self, image: np.ndarray, 
                          resize_factor: float = 2.0,
                          apply_blur: bool = True,
                          apply_morphology: bool = True) -> np.ndarray:
        """
        Preprocess image for better OCR results.
        
        Args:
            image: Input image
            resize_factor: Factor to resize image
            apply_blur: Whether to apply Gaussian blur
            apply_morphology: Whether to apply morphological operations
            
        Returns:
            Preprocessed image
        """
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # Resize image
            if resize_factor != 1.0:
                height, width = gray.shape
                new_height = int(height * resize_factor)
                new_width = int(width * resize_factor)
                gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # Apply Gaussian blur to reduce noise
            if apply_blur:
                gray = cv2.GaussianBlur(gray, (3, 3), 0)
            
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # Morphological operations
            if apply_morphology:
                kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
                thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
                thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
            
            return thresh
            
        except Exception as e:
            self.logger.error(f"Error preprocessing image for OCR: {e}")
            return image
