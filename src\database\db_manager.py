"""
Database manager for SQLite operations.
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Optional, Tuple
from contextlib import contextmanager

from .models import EmployeeVehicle, DetectionLog, SystemConfig


class DatabaseManager:
    """Manages SQLite database operations for the number plate detection system."""
    
    def __init__(self, db_path: str = "data/employee_vehicles.db"):
        """
        Initialize database manager.
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = db_path
        self._ensure_directory_exists()
        self._initialize_database()
    
    def _ensure_directory_exists(self):
        """Ensure the database directory exists."""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    @contextmanager
    def get_connection(self):
        """Context manager for database connections."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable column access by name
        try:
            yield conn
        finally:
            conn.close()
    
    def _initialize_database(self):
        """Create database tables if they don't exist."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Employee vehicles table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS employee_vehicles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    plate_number TEXT UNIQUE NOT NULL,
                    employee_name TEXT NOT NULL,
                    employee_id TEXT NOT NULL,
                    department TEXT,
                    phone_number TEXT,
                    email TEXT,
                    vehicle_type TEXT,
                    vehicle_model TEXT,
                    vehicle_color TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Detection logs table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS detection_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    plate_number TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    classification TEXT NOT NULL,
                    employee_id TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    image_path TEXT,
                    processing_time REAL,
                    camera_source TEXT,
                    FOREIGN KEY (employee_id) REFERENCES employee_vehicles (employee_id)
                )
            """)
            
            # System configuration table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS system_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT NOT NULL,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
    
    # Employee Vehicle CRUD Operations
    def add_employee_vehicle(self, vehicle: EmployeeVehicle) -> int:
        """
        Add a new employee vehicle to the database.
        
        Args:
            vehicle: EmployeeVehicle object
            
        Returns:
            ID of the inserted record
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO employee_vehicles 
                (plate_number, employee_name, employee_id, department, 
                 phone_number, email, vehicle_type, vehicle_model, 
                 vehicle_color, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                vehicle.plate_number, vehicle.employee_name, vehicle.employee_id,
                vehicle.department, vehicle.phone_number, vehicle.email,
                vehicle.vehicle_type, vehicle.vehicle_model, vehicle.vehicle_color,
                vehicle.is_active
            ))
            conn.commit()
            return cursor.lastrowid
    
    def get_employee_vehicle_by_plate(self, plate_number: str) -> Optional[EmployeeVehicle]:
        """
        Get employee vehicle by plate number.
        
        Args:
            plate_number: Vehicle plate number
            
        Returns:
            EmployeeVehicle object or None if not found
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM employee_vehicles 
                WHERE plate_number = ? AND is_active = 1
            """, (plate_number,))
            
            row = cursor.fetchone()
            if row:
                return EmployeeVehicle(
                    id=row['id'],
                    plate_number=row['plate_number'],
                    employee_name=row['employee_name'],
                    employee_id=row['employee_id'],
                    department=row['department'],
                    phone_number=row['phone_number'],
                    email=row['email'],
                    vehicle_type=row['vehicle_type'],
                    vehicle_model=row['vehicle_model'],
                    vehicle_color=row['vehicle_color'],
                    is_active=bool(row['is_active']),
                    created_at=datetime.fromisoformat(row['created_at']),
                    updated_at=datetime.fromisoformat(row['updated_at'])
                )
            return None

    def get_all_employee_vehicles(self) -> List[EmployeeVehicle]:
        """
        Get all active employee vehicles.

        Returns:
            List of EmployeeVehicle objects
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM employee_vehicles
                WHERE is_active = 1
                ORDER BY employee_name
            """)

            vehicles = []
            for row in cursor.fetchall():
                vehicles.append(EmployeeVehicle(
                    id=row['id'],
                    plate_number=row['plate_number'],
                    employee_name=row['employee_name'],
                    employee_id=row['employee_id'],
                    department=row['department'],
                    phone_number=row['phone_number'],
                    email=row['email'],
                    vehicle_type=row['vehicle_type'],
                    vehicle_model=row['vehicle_model'],
                    vehicle_color=row['vehicle_color'],
                    is_active=bool(row['is_active']),
                    created_at=datetime.fromisoformat(row['created_at']),
                    updated_at=datetime.fromisoformat(row['updated_at'])
                ))
            return vehicles

    def update_employee_vehicle(self, vehicle: EmployeeVehicle) -> bool:
        """
        Update an existing employee vehicle.

        Args:
            vehicle: EmployeeVehicle object with updated data

        Returns:
            True if update was successful, False otherwise
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE employee_vehicles
                SET employee_name = ?, department = ?, phone_number = ?,
                    email = ?, vehicle_type = ?, vehicle_model = ?,
                    vehicle_color = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (
                vehicle.employee_name, vehicle.department, vehicle.phone_number,
                vehicle.email, vehicle.vehicle_type, vehicle.vehicle_model,
                vehicle.vehicle_color, vehicle.is_active, vehicle.id
            ))
            conn.commit()
            return cursor.rowcount > 0

    def delete_employee_vehicle(self, vehicle_id: int) -> bool:
        """
        Soft delete an employee vehicle (set is_active to False).

        Args:
            vehicle_id: ID of the vehicle to delete

        Returns:
            True if deletion was successful, False otherwise
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE employee_vehicles
                SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (vehicle_id,))
            conn.commit()
            return cursor.rowcount > 0

    # Detection Log Operations
    def add_detection_log(self, log: DetectionLog) -> int:
        """
        Add a detection log entry.

        Args:
            log: DetectionLog object

        Returns:
            ID of the inserted record
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO detection_logs
                (plate_number, confidence, classification, employee_id,
                 image_path, processing_time, camera_source)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                log.plate_number, log.confidence, log.classification,
                log.employee_id, log.image_path, log.processing_time,
                log.camera_source
            ))
            conn.commit()
            return cursor.lastrowid

    def get_recent_detections(self, limit: int = 100) -> List[DetectionLog]:
        """
        Get recent detection logs.

        Args:
            limit: Maximum number of records to return

        Returns:
            List of DetectionLog objects
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM detection_logs
                ORDER BY timestamp DESC
                LIMIT ?
            """, (limit,))

            logs = []
            for row in cursor.fetchall():
                logs.append(DetectionLog(
                    id=row['id'],
                    plate_number=row['plate_number'],
                    confidence=row['confidence'],
                    classification=row['classification'],
                    employee_id=row['employee_id'],
                    timestamp=datetime.fromisoformat(row['timestamp']),
                    image_path=row['image_path'],
                    processing_time=row['processing_time'],
                    camera_source=row['camera_source']
                ))
            return logs

    # Utility Methods
    def get_statistics(self) -> dict:
        """
        Get system statistics.

        Returns:
            Dictionary with various statistics
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # Total employees
            cursor.execute("SELECT COUNT(*) FROM employee_vehicles WHERE is_active = 1")
            total_employees = cursor.fetchone()[0]

            # Total detections today
            cursor.execute("""
                SELECT COUNT(*) FROM detection_logs
                WHERE DATE(timestamp) = DATE('now')
            """)
            detections_today = cursor.fetchone()[0]

            # Employee vs Visitor detections today
            cursor.execute("""
                SELECT classification, COUNT(*)
                FROM detection_logs
                WHERE DATE(timestamp) = DATE('now')
                GROUP BY classification
            """)
            classification_stats = dict(cursor.fetchall())

            return {
                'total_employees': total_employees,
                'detections_today': detections_today,
                'employee_detections_today': classification_stats.get('Employee', 0),
                'visitor_detections_today': classification_stats.get('Visitor', 0),
                'unknown_detections_today': classification_stats.get('Unknown', 0)
            }

    def backup_database(self, backup_path: str) -> bool:
        """
        Create a backup of the database.

        Args:
            backup_path: Path for the backup file

        Returns:
            True if backup was successful, False otherwise
        """
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            return True
        except Exception as e:
            print(f"Backup failed: {e}")
            return False
