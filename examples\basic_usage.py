#!/usr/bin/env python3
"""
Basic usage example for the Number Plate Detection System.
"""

import sys
import os
import cv2
import time

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.detection.plate_detector import PlateDetector
from src.detection.ocr_engine import NumberPlateOCR
from src.classification.classifier import VehicleClassifier
from src.database.db_manager import DatabaseManager
from src.database.models import EmployeeVehicle
from src.utils.config import ConfigManager
from src.utils.logger import Logger


def setup_sample_database():
    """Setup database with sample employee data."""
    db_manager = DatabaseManager("data/example_vehicles.db")
    
    # Add sample employees
    sample_vehicles = [
        EmployeeVehicle(
            plate_number="MH12AB1234",
            employee_name="<PERSON>",
            employee_id="EMP001",
            department="Engineering",
            vehicle_type="Car"
        ),
        EmployeeVehicle(
            plate_number="KA05CD5678",
            employee_name="<PERSON>",
            employee_id="EMP002",
            department="HR",
            vehicle_type="Car"
        ),
        EmployeeVehicle(
            plate_number="TN09EF9012",
            employee_name="Raj Patel",
            employee_id="EMP003",
            department="Finance",
            vehicle_type="Bike"
        )
    ]
    
    for vehicle in sample_vehicles:
        try:
            db_manager.add_employee_vehicle(vehicle)
            print(f"Added: {vehicle.plate_number} - {vehicle.employee_name}")
        except Exception as e:
            print(f"Vehicle {vehicle.plate_number} already exists or error: {e}")
    
    return db_manager


def process_single_image(image_path: str):
    """
    Process a single image for number plate detection.
    
    Args:
        image_path: Path to the image file
    """
    print(f"\nProcessing image: {image_path}")
    
    # Initialize components
    config_manager = ConfigManager("config.yaml")
    logger = Logger("Example")
    
    # Setup database
    db_manager = setup_sample_database()
    
    # Initialize detection components
    plate_detector = PlateDetector(logger=logger)
    ocr_engine = NumberPlateOCR(logger=logger)
    classifier = VehicleClassifier(db_manager, logger=logger)
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        print(f"Error: Could not load image {image_path}")
        return
    
    print(f"Image loaded: {image.shape}")
    
    # Detect plates
    start_time = time.time()
    detections = plate_detector.detect_plates(image)
    detection_time = time.time() - start_time
    
    print(f"Found {len(detections)} potential plates in {detection_time:.3f}s")
    
    # Process each detection
    for i, detection in enumerate(detections):
        print(f"\nProcessing detection {i+1}:")
        print(f"  Confidence: {detection['confidence']:.3f}")
        print(f"  Bounding box: {detection['bbox']}")
        
        # Extract plate image
        plate_image = plate_detector.extract_plate_image(image, detection['bbox'])
        
        # Preprocess for OCR
        processed_plate = ocr_engine.preprocess_for_ocr(plate_image)
        
        # Extract text
        start_time = time.time()
        plate_text = ocr_engine.extract_plate_text(processed_plate)
        ocr_time = time.time() - start_time
        
        print(f"  OCR Text: '{plate_text}' (extracted in {ocr_time:.3f}s)")
        
        if plate_text:
            # Classify vehicle
            start_time = time.time()
            classification = classifier.classify_vehicle(plate_text, detection['confidence'])
            classification_time = time.time() - start_time
            
            print(f"  Classification: {classification['classification']}")
            print(f"  Match confidence: {classification['match_confidence']:.3f}")
            print(f"  Reason: {classification['reason']}")
            print(f"  Classification time: {classification_time:.3f}s")
            
            if classification['employee_data']:
                emp_data = classification['employee_data']
                print(f"  Employee: {emp_data['employee_name']} ({emp_data['employee_id']})")
                print(f"  Department: {emp_data['department']}")
        
        # Save processed plate image
        output_path = f"output/plate_{i+1}.jpg"
        cv2.imwrite(output_path, plate_image)
        print(f"  Saved plate image: {output_path}")
    
    # Visualize results
    result_image = plate_detector.visualize_detections(image, detections)
    output_path = "output/result_image.jpg"
    cv2.imwrite(output_path, result_image)
    print(f"\nResult image saved: {output_path}")


def process_webcam():
    """Process webcam feed for real-time detection."""
    print("\nStarting webcam processing...")
    print("Press 'q' to quit, 's' to save current frame")
    
    # Initialize components
    config_manager = ConfigManager("config.yaml")
    logger = Logger("WebcamExample")
    
    # Setup database
    db_manager = setup_sample_database()
    
    # Initialize detection components
    plate_detector = PlateDetector(logger=logger)
    ocr_engine = NumberPlateOCR(logger=logger)
    classifier = VehicleClassifier(db_manager, logger=logger)
    
    # Initialize webcam
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("Error: Could not open webcam")
        return
    
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("Error: Could not read frame")
            break
        
        frame_count += 1
        
        # Process every 10th frame to reduce load
        if frame_count % 10 == 0:
            # Detect plates
            detections = plate_detector.detect_plates(frame)
            
            # Process detections
            for detection in detections:
                plate_image = plate_detector.extract_plate_image(frame, detection['bbox'])
                processed_plate = ocr_engine.preprocess_for_ocr(plate_image)
                plate_text = ocr_engine.extract_plate_text(processed_plate)
                
                if plate_text:
                    classification = classifier.classify_vehicle(plate_text, detection['confidence'])
                    
                    # Add text overlay
                    bbox = detection['bbox']
                    label = f"{plate_text} ({classification['classification']})"
                    cv2.putText(frame, label, (bbox[0], bbox[1] - 10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # Draw detections
            frame = plate_detector.visualize_detections(frame, detections)
        
        # Display frame
        cv2.imshow('Number Plate Detection', frame)
        
        # Handle key presses
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            # Save current frame
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"output/webcam_capture_{timestamp}.jpg"
            cv2.imwrite(filename, frame)
            print(f"Frame saved: {filename}")
    
    # Cleanup
    cap.release()
    cv2.destroyAllWindows()
    print("Webcam processing stopped")


def test_database_operations():
    """Test database operations."""
    print("\nTesting database operations...")
    
    db_manager = setup_sample_database()
    
    # Test retrieval
    print("\nAll employee vehicles:")
    vehicles = db_manager.get_all_employee_vehicles()
    for vehicle in vehicles:
        print(f"  {vehicle.plate_number} - {vehicle.employee_name} ({vehicle.department})")
    
    # Test lookup
    test_plates = ["MH12AB1234", "KA05CD5678", "UNKNOWN123"]
    for plate in test_plates:
        vehicle = db_manager.get_employee_vehicle_by_plate(plate)
        if vehicle:
            print(f"\nFound: {plate} -> {vehicle.employee_name}")
        else:
            print(f"\nNot found: {plate}")
    
    # Test statistics
    stats = db_manager.get_statistics()
    print(f"\nDatabase statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")


def main():
    """Main function to run examples."""
    print("Number Plate Detection System - Basic Usage Examples")
    print("=" * 60)
    
    # Create output directory
    os.makedirs("output", exist_ok=True)
    
    # Test database operations
    test_database_operations()
    
    # Check if we have a test image
    test_image = "examples/test_image.jpg"
    if os.path.exists(test_image):
        process_single_image(test_image)
    else:
        print(f"\nTest image not found: {test_image}")
        print("Place a test image with number plates in the examples/ directory")
    
    # Ask user if they want to test webcam
    response = input("\nDo you want to test webcam detection? (y/n): ")
    if response.lower() == 'y':
        process_webcam()
    
    print("\nExample completed!")


if __name__ == "__main__":
    main()
