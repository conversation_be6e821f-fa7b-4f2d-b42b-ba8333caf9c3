#!/usr/bin/env python3
"""
Setup script for the Real-Time Number Plate Detection System.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required.")
        print(f"Current version: {sys.version}")
        return False
    return True


def create_directories():
    """Create necessary directories."""
    directories = [
        'data',
        'models',
        'output',
        'logs',
        'examples',
        'tests'
    ]
    
    print("Creating directories...")
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"  ✓ {directory}/")


def install_dependencies():
    """Install required dependencies."""
    print("\nInstalling dependencies...")

    # Check if pip is available
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"],
                      check=True, capture_output=True)
    except subprocess.CalledProcessError:
        print("Error: pip is not available. Please install pip first.")
        return False

    # Upgrade pip first
    try:
        print("Upgrading pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"],
                      check=True, capture_output=True)
        print("  ✓ pip upgraded successfully")
    except subprocess.CalledProcessError:
        print("  ⚠ Warning: Could not upgrade pip, continuing with current version")

    # Install requirements
    try:
        print("Installing Python packages...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                      check=True)
        print("  ✓ Python packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        print("💡 Common solutions:")
        print("   - Try: pip install -r requirements.txt")
        print("   - Check your internet connection")
        print("   - Try using a virtual environment")
        print("   - For Windows: Make sure you have Visual C++ Build Tools installed")
        return False


def download_yolo_model():
    """Download YOLOv8 model if not present."""
    model_path = Path("models/yolov8n.pt")
    
    if model_path.exists():
        print(f"  ✓ YOLOv8 model already exists: {model_path}")
        return True
    
    print("\nDownloading YOLOv8 model...")
    try:
        # Import ultralytics to trigger model download
        import ultralytics
        from ultralytics import YOLO
        
        # This will download the model to the default location
        model = YOLO('yolov8n.pt')
        
        # Copy to our models directory
        import shutil
        default_model_path = Path.home() / '.ultralytics' / 'models' / 'yolov8n.pt'
        if default_model_path.exists():
            shutil.copy2(default_model_path, model_path)
            print(f"  ✓ YOLOv8 model downloaded: {model_path}")
        else:
            print(f"  ⚠ Model downloaded but not found at expected location")
        
        return True
    except ImportError:
        print("  ⚠ Ultralytics not installed. Model will be downloaded on first run.")
        return True
    except Exception as e:
        print(f"  ⚠ Error downloading model: {e}")
        print("  Model will be downloaded automatically on first run.")
        return True


def setup_database():
    """Initialize the database with sample data."""
    print("\nSetting up database...")
    
    try:
        # Import database components
        sys.path.insert(0, 'src')
        from src.database.db_manager import DatabaseManager
        from src.database.models import EmployeeVehicle
        
        # Initialize database
        db_path = "data/employee_vehicles.db"
        db_manager = DatabaseManager(db_path)
        
        # Check if database already has data
        existing_vehicles = db_manager.get_all_employee_vehicles()
        if existing_vehicles:
            print(f"  ✓ Database already contains {len(existing_vehicles)} vehicles")
            return True
        
        # Add sample data
        sample_vehicles = [
            EmployeeVehicle(
                plate_number="MH12AB1234",
                employee_name="John Doe",
                employee_id="EMP001",
                department="Engineering",
                phone_number="+91-9876543210",
                email="<EMAIL>",
                vehicle_type="Car",
                vehicle_model="Honda City",
                vehicle_color="White"
            ),
            EmployeeVehicle(
                plate_number="MH14CD5678",
                employee_name="Jane Smith",
                employee_id="EMP002",
                department="HR",
                phone_number="+91-9876543211",
                email="<EMAIL>",
                vehicle_type="Car",
                vehicle_model="Maruti Swift",
                vehicle_color="Red"
            ),
            EmployeeVehicle(
                plate_number="KA05EF9012",
                employee_name="Raj Patel",
                employee_id="EMP003",
                department="Finance",
                phone_number="+91-9876543212",
                email="<EMAIL>",
                vehicle_type="Bike",
                vehicle_model="Honda Activa",
                vehicle_color="Black"
            ),
            EmployeeVehicle(
                plate_number="TN09GH3456",
                employee_name="Priya Sharma",
                employee_id="EMP004",
                department="Marketing",
                phone_number="+91-9876543213",
                email="<EMAIL>",
                vehicle_type="Car",
                vehicle_model="Hyundai i20",
                vehicle_color="Blue"
            ),
            EmployeeVehicle(
                plate_number="DL01IJ7890",
                employee_name="Amit Kumar",
                employee_id="EMP005",
                department="Operations",
                phone_number="+91-9876543214",
                email="<EMAIL>",
                vehicle_type="SUV",
                vehicle_model="Mahindra XUV500",
                vehicle_color="Silver"
            )
        ]
        
        for vehicle in sample_vehicles:
            db_manager.add_employee_vehicle(vehicle)
        
        print(f"  ✓ Database initialized with {len(sample_vehicles)} sample vehicles")
        return True
        
    except Exception as e:
        print(f"  ⚠ Error setting up database: {e}")
        return False


def verify_installation():
    """Verify that the installation is working."""
    print("\nVerifying installation...")
    
    try:
        # Test imports
        sys.path.insert(0, 'src')
        
        from src.utils.config import ConfigManager
        from src.utils.logger import Logger
        from src.database.db_manager import DatabaseManager
        
        # Test configuration
        config_manager = ConfigManager("config.yaml")
        if not config_manager.validate_config():
            print("  ⚠ Configuration validation failed")
            return False
        
        # Test database
        db_manager = DatabaseManager("data/employee_vehicles.db")
        vehicles = db_manager.get_all_employee_vehicles()
        
        print(f"  ✓ Configuration loaded successfully")
        print(f"  ✓ Database accessible with {len(vehicles)} vehicles")
        print(f"  ✓ All core components working")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Verification failed: {e}")
        return False


def print_usage_instructions():
    """Print usage instructions."""
    print("\n" + "="*60)
    print("INSTALLATION COMPLETE!")
    print("="*60)
    
    print("\nQuick Start:")
    print("  1. Run the GUI application:")
    print("     python main.py")
    print()
    print("  2. Run with webcam:")
    print("     python main.py --source 0")
    print()
    print("  3. Process a video file:")
    print("     python main.py --source path/to/video.mp4")
    print()
    print("  4. Command line mode:")
    print("     python main.py --no-gui --list-employees")
    print()
    print("  5. Add employee vehicle:")
    print("     python main.py --no-gui --add-employee MH12XY9999 \"Employee Name\" EMP999")
    print()
    print("  6. Run tests:")
    print("     python tests/test_detection.py")
    print()
    print("  7. Run examples:")
    print("     python examples/basic_usage.py")
    
    print("\nConfiguration:")
    print("  - Edit config.yaml to customize settings")
    print("  - Check logs/ directory for application logs")
    print("  - Output images saved to output/ directory")
    
    print("\nDatabase:")
    print("  - Employee data stored in data/employee_vehicles.db")
    print("  - Use the GUI or command line to manage employee vehicles")
    
    print("\nFor more information, see README.md")
    print("="*60)


def main():
    """Main setup function."""
    print("Real-Time Number Plate Detection System Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    print(f"Python version: {sys.version.split()[0]} ✓")
    print(f"Platform: {platform.system()} {platform.release()}")
    
    # Create directories
    create_directories()
    
    # Install dependencies
    if not install_dependencies():
        print("\nSetup failed during dependency installation.")
        sys.exit(1)
    
    # Download YOLO model
    download_yolo_model()
    
    # Setup database
    setup_database()
    
    # Verify installation
    if not verify_installation():
        print("\nSetup completed with warnings. Some components may not work correctly.")
        print("Please check the error messages above and resolve any issues.")
    else:
        print("\n✓ Setup completed successfully!")
    
    # Print usage instructions
    print_usage_instructions()


if __name__ == "__main__":
    main()
