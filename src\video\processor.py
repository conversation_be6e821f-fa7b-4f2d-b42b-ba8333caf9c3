"""
Real-time video processing for number plate detection.
"""

import cv2
import numpy as np
import time
import os
from typing import Optional, Callable, Dict, Any, List, Tuple
from threading import Thread, Event
from queue import Queue, Empty
from datetime import datetime

from ..detection.plate_detector import PlateDetector
from ..detection.ocr_engine import NumberPlateOCR
from ..classification.classifier import VehicleClassifier
from ..utils.logger import Logger


class VideoProcessor:
    """Real-time video processor for number plate detection."""
    
    def __init__(self, plate_detector: PlateDetector,
                 ocr_engine: NumberPlateOCR,
                 classifier: VehicleClassifier,
                 logger: Optional[Logger] = None):
        """
        Initialize video processor.
        
        Args:
            plate_detector: Plate detection engine
            ocr_engine: OCR engine
            classifier: Vehicle classifier
            logger: Logger instance
        """
        self.plate_detector = plate_detector
        self.ocr_engine = ocr_engine
        self.classifier = classifier
        self.logger = logger or Logger()
        
        # Video capture
        self.cap = None
        self.is_running = False
        self.stop_event = Event()
        
        # Processing settings
        self.input_source = 0
        self.output_enabled = False
        self.output_path = "output/"
        self.fps = 30
        self.resolution = (1280, 720)
        
        # ROI settings
        self.roi_enabled = False
        self.roi_coordinates = [100, 100, 500, 400]
        
        # Performance tracking
        self.frame_count = 0
        self.start_time = None
        self.processing_times = []
        
        # Callback for results
        self.result_callback = None
        
        # Frame queue for processing
        self.frame_queue = Queue(maxsize=10)
        self.result_queue = Queue(maxsize=50)
        
        self.logger.info("Video processor initialized")
    
    def configure(self, config: Dict[str, Any]) -> None:
        """
        Configure video processor settings.
        
        Args:
            config: Configuration dictionary
        """
        video_config = config.get('video', {})
        detection_config = config.get('detection', {})
        
        self.input_source = video_config.get('input_source', 0)
        self.output_enabled = video_config.get('output_enabled', False)
        self.output_path = video_config.get('output_path', 'output/')
        self.fps = video_config.get('fps', 30)
        
        resolution = video_config.get('resolution', {})
        self.resolution = (
            resolution.get('width', 1280),
            resolution.get('height', 720)
        )
        
        self.roi_enabled = detection_config.get('roi_enabled', False)
        self.roi_coordinates = detection_config.get('roi_coordinates', [100, 100, 500, 400])
        
        self.logger.info(f"Video processor configured: source={self.input_source}, "
                        f"resolution={self.resolution}, roi_enabled={self.roi_enabled}")
    
    def set_result_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """
        Set callback function for processing results.
        
        Args:
            callback: Function to call with detection results
        """
        self.result_callback = callback
    
    def start_processing(self) -> bool:
        """
        Start video processing.
        
        Returns:
            True if started successfully, False otherwise
        """
        try:
            # Initialize video capture
            if isinstance(self.input_source, int):
                self.cap = cv2.VideoCapture(self.input_source)
                self.logger.info(f"Using camera {self.input_source}")
            else:
                self.cap = cv2.VideoCapture(self.input_source)
                self.logger.info(f"Using video file: {self.input_source}")
            
            if not self.cap.isOpened():
                self.logger.error("Failed to open video source")
                return False
            
            # Set camera properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.resolution[0])
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.resolution[1])
            self.cap.set(cv2.CAP_PROP_FPS, self.fps)
            
            # Create output directory if needed
            if self.output_enabled:
                os.makedirs(self.output_path, exist_ok=True)
            
            # Reset counters
            self.frame_count = 0
            self.start_time = time.time()
            self.processing_times = []
            self.stop_event.clear()
            self.is_running = True
            
            # Start processing thread
            processing_thread = Thread(target=self._processing_loop, daemon=True)
            processing_thread.start()
            
            self.logger.info("Video processing started")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start video processing: {e}")
            return False
    
    def stop_processing(self) -> None:
        """Stop video processing."""
        self.stop_event.set()
        self.is_running = False
        
        if self.cap:
            self.cap.release()
        
        self.logger.info("Video processing stopped")
    
    def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        Process a single frame.
        
        Args:
            frame: Input frame
            
        Returns:
            Processing result dictionary
        """
        start_time = time.time()
        
        try:
            # Apply ROI if enabled
            if self.roi_enabled:
                roi_frame = self._apply_roi(frame)
            else:
                roi_frame = frame
            
            # Detect plates
            detections = self.plate_detector.detect_plates(roi_frame)
            
            results = []
            
            for detection in detections:
                # Extract plate image
                plate_image = self.plate_detector.extract_plate_image(
                    roi_frame, detection['bbox']
                )
                
                # Preprocess for OCR
                processed_plate = self.ocr_engine.preprocess_for_ocr(plate_image)
                
                # Extract text
                plate_text = self.ocr_engine.extract_plate_text(processed_plate)
                
                if plate_text:
                    # Classify vehicle
                    classification = self.classifier.classify_vehicle(
                        plate_text, detection['confidence']
                    )
                    
                    # Adjust bbox coordinates if ROI was used
                    if self.roi_enabled:
                        detection['bbox'] = self._adjust_bbox_for_roi(detection['bbox'])
                    
                    result = {
                        'bbox': detection['bbox'],
                        'plate_text': plate_text,
                        'detection_confidence': detection['confidence'],
                        'classification': classification,
                        'timestamp': datetime.now()
                    }
                    results.append(result)
            
            processing_time = time.time() - start_time
            self.processing_times.append(processing_time)
            
            # Keep only last 100 processing times
            if len(self.processing_times) > 100:
                self.processing_times = self.processing_times[-100:]
            
            return {
                'frame': frame,
                'detections': results,
                'processing_time': processing_time,
                'frame_count': self.frame_count
            }
            
        except Exception as e:
            self.logger.error(f"Error processing frame: {e}")
            return {
                'frame': frame,
                'detections': [],
                'processing_time': time.time() - start_time,
                'frame_count': self.frame_count,
                'error': str(e)
            }
    
    def _processing_loop(self) -> None:
        """Main processing loop running in separate thread."""
        while not self.stop_event.is_set() and self.cap and self.cap.isOpened():
            try:
                ret, frame = self.cap.read()
                
                if not ret:
                    if isinstance(self.input_source, str):  # Video file ended
                        self.logger.info("Video file processing completed")
                        break
                    else:  # Camera error
                        self.logger.warning("Failed to read frame from camera")
                        continue
                
                self.frame_count += 1
                
                # Process frame
                result = self.process_frame(frame)
                
                # Call result callback if set
                if self.result_callback:
                    try:
                        self.result_callback(result)
                    except Exception as e:
                        self.logger.error(f"Error in result callback: {e}")
                
                # Add to result queue
                try:
                    self.result_queue.put_nowait(result)
                except:
                    # Queue full, remove oldest item
                    try:
                        self.result_queue.get_nowait()
                        self.result_queue.put_nowait(result)
                    except Empty:
                        pass
                
                # Save frame if output enabled
                if self.output_enabled and result['detections']:
                    self._save_detection_frame(frame, result)
                
            except Exception as e:
                self.logger.error(f"Error in processing loop: {e}")
                time.sleep(0.1)  # Brief pause before continuing
        
        self.is_running = False
    
    def _apply_roi(self, frame: np.ndarray) -> np.ndarray:
        """
        Apply Region of Interest to frame.
        
        Args:
            frame: Input frame
            
        Returns:
            ROI frame
        """
        x1, y1, x2, y2 = self.roi_coordinates
        h, w = frame.shape[:2]
        
        # Ensure coordinates are within frame bounds
        x1 = max(0, min(x1, w))
        y1 = max(0, min(y1, h))
        x2 = max(x1, min(x2, w))
        y2 = max(y1, min(y2, h))
        
        return frame[y1:y2, x1:x2]
    
    def _adjust_bbox_for_roi(self, bbox: List[int]) -> List[int]:
        """
        Adjust bounding box coordinates for ROI offset.
        
        Args:
            bbox: Bounding box [x1, y1, x2, y2]
            
        Returns:
            Adjusted bounding box
        """
        x1, y1, x2, y2 = bbox
        roi_x1, roi_y1, _, _ = self.roi_coordinates
        
        return [x1 + roi_x1, y1 + roi_y1, x2 + roi_x1, y2 + roi_y1]
    
    def _save_detection_frame(self, frame: np.ndarray, result: Dict[str, Any]) -> None:
        """
        Save frame with detections to output directory.
        
        Args:
            frame: Original frame
            result: Processing result
        """
        try:
            # Draw detections on frame
            output_frame = self.visualize_detections(frame, result['detections'])
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"detection_{timestamp}.jpg"
            filepath = os.path.join(self.output_path, filename)
            
            # Save frame
            cv2.imwrite(filepath, output_frame)
            
        except Exception as e:
            self.logger.error(f"Error saving detection frame: {e}")
    
    def visualize_detections(self, frame: np.ndarray, 
                           detections: List[Dict[str, Any]]) -> np.ndarray:
        """
        Draw detection results on frame.
        
        Args:
            frame: Input frame
            detections: List of detection results
            
        Returns:
            Frame with drawn detections
        """
        result_frame = frame.copy()
        
        for detection in detections:
            bbox = detection['bbox']
            plate_text = detection['plate_text']
            classification = detection['classification']['classification']
            confidence = detection['detection_confidence']
            
            x1, y1, x2, y2 = bbox
            
            # Choose color based on classification
            if classification == "Employee":
                color = (0, 255, 0)  # Green
            elif classification == "Visitor":
                color = (0, 165, 255)  # Orange
            else:
                color = (0, 0, 255)  # Red
            
            # Draw bounding box
            cv2.rectangle(result_frame, (x1, y1), (x2, y2), color, 2)
            
            # Prepare label
            label = f"{plate_text} ({classification})"
            if confidence:
                label += f" {confidence:.2f}"
            
            # Draw label background
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            cv2.rectangle(result_frame, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), color, -1)
            
            # Draw label text
            cv2.putText(result_frame, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # Draw ROI if enabled
        if self.roi_enabled:
            x1, y1, x2, y2 = self.roi_coordinates
            cv2.rectangle(result_frame, (x1, y1), (x2, y2), (255, 255, 0), 2)
            cv2.putText(result_frame, "ROI", (x1, y1 - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
        
        return result_frame
    
    def get_latest_result(self) -> Optional[Dict[str, Any]]:
        """
        Get the latest processing result.
        
        Returns:
            Latest result or None if queue is empty
        """
        try:
            return self.result_queue.get_nowait()
        except Empty:
            return None
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics.
        
        Returns:
            Performance statistics dictionary
        """
        if not self.processing_times:
            return {}
        
        avg_processing_time = sum(self.processing_times) / len(self.processing_times)
        max_processing_time = max(self.processing_times)
        min_processing_time = min(self.processing_times)
        
        elapsed_time = time.time() - self.start_time if self.start_time else 0
        fps = self.frame_count / elapsed_time if elapsed_time > 0 else 0
        
        return {
            'frames_processed': self.frame_count,
            'elapsed_time': elapsed_time,
            'fps': fps,
            'avg_processing_time': avg_processing_time,
            'max_processing_time': max_processing_time,
            'min_processing_time': min_processing_time,
            'is_running': self.is_running
        }
