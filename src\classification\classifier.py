"""
Vehicle classification system for Employee/Visitor identification.
"""

import re
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime, timedelta
from difflib import SequenceMatcher

from ..database.db_manager import DatabaseManager
from ..database.models import EmployeeVehicle, DetectionLog
from ..utils.logger import Logger


class VehicleClassifier:
    """Classifies vehicles as Employee or Visitor based on database lookup."""
    
    def __init__(self, db_manager: DatabaseManager,
                 match_threshold: float = 0.8,
                 logger: Optional[Logger] = None):
        """
        Initialize vehicle classifier.
        
        Args:
            db_manager: Database manager instance
            match_threshold: Minimum similarity threshold for plate matching
            logger: Logger instance
        """
        self.db_manager = db_manager
        self.match_threshold = match_threshold
        self.logger = logger or Logger()
        
        # Cache for recent lookups to improve performance
        self._cache = {}
        self._cache_timeout = 300  # 5 minutes
        
        self.logger.info("Vehicle classifier initialized")
    
    def classify_vehicle(self, plate_number: str, confidence: float = 1.0) -> Dict[str, Any]:
        """
        Classify vehicle based on plate number.
        
        Args:
            plate_number: Detected plate number
            confidence: OCR confidence score
            
        Returns:
            Classification result dictionary
        """
        try:
            # Clean the plate number
            cleaned_plate = self._clean_plate_number(plate_number)
            
            if not cleaned_plate:
                return self._create_result("Unknown", None, 0.0, "Invalid plate number")
            
            # Check cache first
            cache_key = cleaned_plate
            if cache_key in self._cache:
                cache_entry = self._cache[cache_key]
                if datetime.now() - cache_entry['timestamp'] < timedelta(seconds=self._cache_timeout):
                    self.logger.debug(f"Using cached result for plate: {cleaned_plate}")
                    return cache_entry['result']
            
            # Exact match lookup
            employee_vehicle = self.db_manager.get_employee_vehicle_by_plate(cleaned_plate)
            
            if employee_vehicle:
                result = self._create_result(
                    "Employee", 
                    employee_vehicle, 
                    1.0, 
                    "Exact match found"
                )
            else:
                # Try fuzzy matching for potential OCR errors
                fuzzy_result = self._fuzzy_match_plate(cleaned_plate)
                if fuzzy_result:
                    result = fuzzy_result
                else:
                    result = self._create_result("Visitor", None, 0.0, "No match found")
            
            # Cache the result
            self._cache[cache_key] = {
                'result': result,
                'timestamp': datetime.now()
            }
            
            # Log the detection
            self._log_detection(cleaned_plate, confidence, result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error classifying vehicle: {e}")
            return self._create_result("Unknown", None, 0.0, f"Classification error: {str(e)}")
    
    def _clean_plate_number(self, plate_number: str) -> str:
        """
        Clean and standardize plate number format.
        
        Args:
            plate_number: Raw plate number
            
        Returns:
            Cleaned plate number
        """
        if not plate_number:
            return ""
        
        # Remove spaces and convert to uppercase
        cleaned = re.sub(r'\s+', '', plate_number.upper())
        
        # Remove special characters except alphanumeric
        cleaned = re.sub(r'[^A-Z0-9]', '', cleaned)
        
        # Handle common OCR errors
        cleaned = self._fix_common_ocr_errors(cleaned)
        
        return cleaned
    
    def _fix_common_ocr_errors(self, plate_text: str) -> str:
        """
        Fix common OCR errors in plate text.
        
        Args:
            plate_text: Plate text with potential OCR errors
            
        Returns:
            Corrected plate text
        """
        # Common OCR substitutions
        substitutions = {
            '0': 'O',  # Zero to O in letter positions
            'O': '0',  # O to zero in number positions
            '1': 'I',  # One to I in letter positions
            'I': '1',  # I to one in number positions
            '5': 'S',  # Five to S in letter positions
            'S': '5',  # S to five in number positions
            '8': 'B',  # Eight to B in letter positions
            'B': '8',  # B to eight in number positions
        }
        
        # Apply corrections based on Indian plate format
        # Format: XX##XX#### or XX##X#### or XX###X###
        if len(plate_text) >= 8:
            corrected = list(plate_text)
            
            # First two characters should be letters
            for i in range(2):
                if i < len(corrected) and corrected[i].isdigit():
                    if corrected[i] in substitutions:
                        corrected[i] = substitutions[corrected[i]]
            
            # Apply format-specific corrections
            if len(plate_text) == 10:  # XX##XX####
                # Positions 2-3 should be digits
                for i in range(2, 4):
                    if i < len(corrected) and corrected[i].isalpha():
                        if corrected[i] in substitutions:
                            corrected[i] = substitutions[corrected[i]]
                
                # Positions 4-5 should be letters
                for i in range(4, 6):
                    if i < len(corrected) and corrected[i].isdigit():
                        if corrected[i] in substitutions:
                            corrected[i] = substitutions[corrected[i]]
                
                # Positions 6-9 should be digits
                for i in range(6, 10):
                    if i < len(corrected) and corrected[i].isalpha():
                        if corrected[i] in substitutions:
                            corrected[i] = substitutions[corrected[i]]
            
            return ''.join(corrected)
        
        return plate_text
    
    def _fuzzy_match_plate(self, plate_number: str) -> Optional[Dict[str, Any]]:
        """
        Perform fuzzy matching against employee database.
        
        Args:
            plate_number: Plate number to match
            
        Returns:
            Classification result if match found, None otherwise
        """
        try:
            # Get all employee vehicles
            all_vehicles = self.db_manager.get_all_employee_vehicles()
            
            best_match = None
            best_similarity = 0.0
            
            for vehicle in all_vehicles:
                similarity = SequenceMatcher(None, plate_number, vehicle.plate_number).ratio()
                
                if similarity > best_similarity and similarity >= self.match_threshold:
                    best_similarity = similarity
                    best_match = vehicle
            
            if best_match:
                self.logger.info(
                    f"Fuzzy match found: {plate_number} -> {best_match.plate_number} "
                    f"(similarity: {best_similarity:.2f})"
                )
                return self._create_result(
                    "Employee", 
                    best_match, 
                    best_similarity, 
                    f"Fuzzy match (similarity: {best_similarity:.2f})"
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error in fuzzy matching: {e}")
            return None
    
    def _create_result(self, classification: str, 
                      employee_vehicle: Optional[EmployeeVehicle],
                      match_confidence: float,
                      reason: str) -> Dict[str, Any]:
        """
        Create classification result dictionary.
        
        Args:
            classification: Classification result (Employee/Visitor/Unknown)
            employee_vehicle: Employee vehicle data if found
            match_confidence: Confidence of the match
            reason: Reason for classification
            
        Returns:
            Classification result dictionary
        """
        result = {
            'classification': classification,
            'match_confidence': match_confidence,
            'reason': reason,
            'timestamp': datetime.now(),
            'employee_data': None
        }
        
        if employee_vehicle:
            result['employee_data'] = {
                'employee_id': employee_vehicle.employee_id,
                'employee_name': employee_vehicle.employee_name,
                'department': employee_vehicle.department,
                'plate_number': employee_vehicle.plate_number,
                'vehicle_type': employee_vehicle.vehicle_type,
                'vehicle_model': employee_vehicle.vehicle_model,
                'phone_number': employee_vehicle.phone_number,
                'email': employee_vehicle.email
            }
        
        return result
    
    def _log_detection(self, plate_number: str, ocr_confidence: float, 
                      classification_result: Dict[str, Any]) -> None:
        """
        Log detection to database.
        
        Args:
            plate_number: Detected plate number
            ocr_confidence: OCR confidence score
            classification_result: Classification result
        """
        try:
            employee_id = None
            if classification_result['employee_data']:
                employee_id = classification_result['employee_data']['employee_id']
            
            log_entry = DetectionLog(
                plate_number=plate_number,
                confidence=ocr_confidence,
                classification=classification_result['classification'],
                employee_id=employee_id,
                processing_time=0.0,  # Will be updated by caller
                camera_source="default"
            )
            
            self.db_manager.add_detection_log(log_entry)
            
        except Exception as e:
            self.logger.error(f"Error logging detection: {e}")
    
    def get_recent_detections(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get recent detection results.
        
        Args:
            limit: Maximum number of results to return
            
        Returns:
            List of recent detections
        """
        try:
            logs = self.db_manager.get_recent_detections(limit)
            
            results = []
            for log in logs:
                result = {
                    'id': log.id,
                    'plate_number': log.plate_number,
                    'confidence': log.confidence,
                    'classification': log.classification,
                    'employee_id': log.employee_id,
                    'timestamp': log.timestamp,
                    'processing_time': log.processing_time
                }
                results.append(result)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error getting recent detections: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get classification statistics.
        
        Returns:
            Statistics dictionary
        """
        try:
            return self.db_manager.get_statistics()
        except Exception as e:
            self.logger.error(f"Error getting statistics: {e}")
            return {}
    
    def clear_cache(self) -> None:
        """Clear the classification cache."""
        self._cache.clear()
        self.logger.info("Classification cache cleared")
    
    def update_match_threshold(self, threshold: float) -> None:
        """
        Update the fuzzy match threshold.
        
        Args:
            threshold: New threshold value (0.0 to 1.0)
        """
        if 0.0 <= threshold <= 1.0:
            self.match_threshold = threshold
            self.logger.info(f"Match threshold updated to {threshold}")
        else:
            self.logger.warning(f"Invalid threshold value: {threshold}")
    
    def add_employee_vehicle(self, plate_number: str, employee_name: str,
                           employee_id: str, department: str = "",
                           **kwargs) -> bool:
        """
        Add new employee vehicle to database.
        
        Args:
            plate_number: Vehicle plate number
            employee_name: Employee name
            employee_id: Employee ID
            department: Department name
            **kwargs: Additional vehicle information
            
        Returns:
            True if added successfully, False otherwise
        """
        try:
            vehicle = EmployeeVehicle(
                plate_number=self._clean_plate_number(plate_number),
                employee_name=employee_name,
                employee_id=employee_id,
                department=department,
                phone_number=kwargs.get('phone_number', ''),
                email=kwargs.get('email', ''),
                vehicle_type=kwargs.get('vehicle_type', ''),
                vehicle_model=kwargs.get('vehicle_model', ''),
                vehicle_color=kwargs.get('vehicle_color', '')
            )
            
            vehicle_id = self.db_manager.add_employee_vehicle(vehicle)
            
            if vehicle_id:
                self.logger.info(f"Added employee vehicle: {plate_number} for {employee_name}")
                self.clear_cache()  # Clear cache to include new vehicle
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error adding employee vehicle: {e}")
            return False
