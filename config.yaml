# Configuration file for Real-Time Number Plate Detection System

# Model Configuration
model:
  yolo_model_path: "models/yolov8n.pt"  # YOLOv8 model path
  confidence_threshold: 0.5
  iou_threshold: 0.4
  device: "cpu"  # or "cuda" for GPU

# OCR Configuration
ocr:
  engine: "easyocr"  # "easyocr" or "tesseract"
  languages: ["en"]
  confidence_threshold: 0.6
  preprocessing:
    resize_factor: 2.0
    gaussian_blur: true
    morphology: true

# Database Configuration
database:
  type: "sqlite"
  path: "data/employee_vehicles.db"
  backup_enabled: true
  backup_interval: 24  # hours

# Video Processing
video:
  input_source: 0  # 0 for webcam, or path to video file
  output_enabled: true
  output_path: "output/"
  fps: 30
  resolution:
    width: 1280
    height: 720

# Detection Parameters
detection:
  roi_enabled: false  # Region of Interest
  roi_coordinates: [100, 100, 500, 400]  # [x1, y1, x2, y2]
  tracking_enabled: true
  min_plate_area: 1000
  max_plate_area: 50000

# Classification
classification:
  employee_match_threshold: 0.8
  visitor_log_enabled: true
  notification_enabled: false

# Logging
logging:
  level: "INFO"
  file_enabled: true
  file_path: "logs/system.log"
  max_file_size: "10MB"
  backup_count: 5

# UI Configuration
ui:
  window_title: "Real-Time Number Plate Detection"
  theme: "dark"
  show_confidence: true
  show_processing_time: true
  alert_sound: false
