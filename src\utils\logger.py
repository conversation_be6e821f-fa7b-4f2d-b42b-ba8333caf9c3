"""
Logging utilities for the number plate detection system.
"""

import logging
import os
from datetime import datetime
from typing import Optional
from logging.handlers import RotatingFileHandler


class Logger:
    """Custom logger for the application."""
    
    def __init__(self, name: str = "NumberPlateDetection", 
                 log_file: Optional[str] = None,
                 log_level: str = "INFO",
                 max_file_size: str = "10MB",
                 backup_count: int = 5):
        """
        Initialize logger.
        
        Args:
            name: Logger name
            log_file: Path to log file (optional)
            log_level: Logging level
            max_file_size: Maximum log file size
            backup_count: Number of backup files to keep
        """
        self.name = name
        self.log_file = log_file
        self.log_level = getattr(logging, log_level.upper(), logging.INFO)
        self.max_file_size = self._parse_file_size(max_file_size)
        self.backup_count = backup_count
        
        self.logger = logging.getLogger(name)
        self.logger.setLevel(self.log_level)
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        self._setup_handlers()
    
    def _parse_file_size(self, size_str: str) -> int:
        """
        Parse file size string to bytes.
        
        Args:
            size_str: Size string (e.g., "10MB", "1GB")
            
        Returns:
            Size in bytes
        """
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def _setup_handlers(self):
        """Setup logging handlers."""
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        
        # Console formatter
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # File handler (if log file specified)
        if self.log_file:
            # Ensure log directory exists
            log_dir = os.path.dirname(self.log_file)
            if log_dir:
                os.makedirs(log_dir, exist_ok=True)
            
            file_handler = RotatingFileHandler(
                self.log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count
            )
            file_handler.setLevel(self.log_level)
            
            # File formatter (more detailed)
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
    
    def debug(self, message: str, *args, **kwargs):
        """Log debug message."""
        self.logger.debug(message, *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs):
        """Log info message."""
        self.logger.info(message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs):
        """Log warning message."""
        self.logger.warning(message, *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs):
        """Log error message."""
        self.logger.error(message, *args, **kwargs)
    
    def critical(self, message: str, *args, **kwargs):
        """Log critical message."""
        self.logger.critical(message, *args, **kwargs)
    
    def exception(self, message: str, *args, **kwargs):
        """Log exception with traceback."""
        self.logger.exception(message, *args, **kwargs)
    
    def log_detection(self, plate_number: str, confidence: float, 
                     classification: str, processing_time: float):
        """
        Log detection event.
        
        Args:
            plate_number: Detected plate number
            confidence: Detection confidence
            classification: Vehicle classification
            processing_time: Processing time in seconds
        """
        self.info(
            f"Detection: Plate={plate_number}, Confidence={confidence:.2f}, "
            f"Classification={classification}, ProcessingTime={processing_time:.3f}s"
        )
    
    def log_system_event(self, event_type: str, details: str):
        """
        Log system event.
        
        Args:
            event_type: Type of event
            details: Event details
        """
        self.info(f"System Event: {event_type} - {details}")
    
    def log_error_with_context(self, error: Exception, context: str):
        """
        Log error with context information.
        
        Args:
            error: Exception object
            context: Context where error occurred
        """
        self.error(f"Error in {context}: {str(error)}", exc_info=True)
    
    def log_performance(self, operation: str, duration: float, details: str = ""):
        """
        Log performance metrics.
        
        Args:
            operation: Operation name
            duration: Duration in seconds
            details: Additional details
        """
        self.info(f"Performance: {operation} took {duration:.3f}s {details}")
    
    @classmethod
    def get_logger(cls, name: str = "NumberPlateDetection", 
                   config: Optional[dict] = None) -> 'Logger':
        """
        Get logger instance with configuration.
        
        Args:
            name: Logger name
            config: Configuration dictionary
            
        Returns:
            Logger instance
        """
        if config is None:
            config = {}
        
        log_config = config.get('logging', {})
        
        return cls(
            name=name,
            log_file=log_config.get('file_path') if log_config.get('file_enabled') else None,
            log_level=log_config.get('level', 'INFO'),
            max_file_size=log_config.get('max_file_size', '10MB'),
            backup_count=log_config.get('backup_count', 5)
        )
