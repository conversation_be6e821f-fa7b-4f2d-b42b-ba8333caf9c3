# Installation Guide

## Quick Fix for Common Installation Issues

### Issue: `sqlite3` and `tkinter` Installation Errors

If you encounter errors like:
```
ERROR: Could not find a version that satisfies the requirement sqlite3
ERROR: Could not find a version that satisfies the requirement tkinter
```

**Solution**: These are built into Python and don't need to be installed via pip.

### Step-by-Step Installation

#### 1. **Verify Python Installation**
```bash
python --version
# Should show Python 3.8 or higher
```

#### 2. **Install Dependencies**
```bash
# Option A: Use the setup script (recommended)
python setup.py

# Option B: Manual installation
pip install -r requirements.txt
```

#### 3. **If Installation Fails**

**For Windows:**
```bash
# Install Visual C++ Build Tools if needed
# Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/

# Try installing with --user flag
pip install --user -r requirements.txt
```

**For macOS:**
```bash
# Install Xcode command line tools
xcode-select --install

# Install dependencies
pip install -r requirements.txt
```

**For Linux (Ubuntu/Debian):**
```bash
# Install system dependencies
sudo apt-get update
sudo apt-get install python3-tk python3-dev build-essential

# Install Python packages
pip install -r requirements.txt
```

#### 4. **Alternative: Use Virtual Environment**
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

#### 5. **Test Installation**
```bash
# Test basic imports
python -c "import cv2, numpy, sqlite3, tkinter; print('All imports successful!')"

# Run the application
python main.py
```

### Troubleshooting

#### Common Issues:

1. **CUDA/GPU Issues**
   - Edit `config.yaml` and set `device: "cpu"`

2. **Tesseract OCR Issues**
   - Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
   - macOS: `brew install tesseract`
   - Linux: `sudo apt-get install tesseract-ocr`

3. **Camera Access Issues**
   - Check camera permissions
   - Try different camera indices (0, 1, 2...)

4. **Model Download Issues**
   - Ensure internet connection
   - Models will be downloaded automatically on first run

### Minimal Installation (Core Features Only)

If you want to install only essential packages:

```bash
pip install ultralytics opencv-python easyocr numpy pillow pyyaml
```

This will give you the core functionality without optional packages.

### Verification

After installation, verify everything works:

```bash
# Run tests
python tests/test_detection.py

# Run basic example
python examples/basic_usage.py

# Start GUI application
python main.py
```

### Getting Help

If you still encounter issues:

1. Check the error message carefully
2. Ensure you have Python 3.8+ installed
3. Try using a virtual environment
4. Check your internet connection for package downloads
5. For Windows users, ensure Visual C++ Build Tools are installed

### System Requirements

- **Python**: 3.8 or higher
- **RAM**: Minimum 4GB (8GB recommended)
- **Storage**: 2GB free space for models and dependencies
- **Internet**: Required for initial setup and model downloads
