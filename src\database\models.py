"""
Database models for the number plate detection system.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass
class EmployeeVehicle:
    """Model for employee vehicle data."""
    
    id: Optional[int] = None
    plate_number: str = ""
    employee_name: str = ""
    employee_id: str = ""
    department: str = ""
    phone_number: str = ""
    email: str = ""
    vehicle_type: str = ""  # car, bike, truck, etc.
    vehicle_model: str = ""
    vehicle_color: str = ""
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Set timestamps if not provided."""
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()


@dataclass
class DetectionLog:
    """Model for detection log entries."""
    
    id: Optional[int] = None
    plate_number: str = ""
    confidence: float = 0.0
    classification: str = ""  # Employee, Visitor, Unknown
    employee_id: Optional[str] = None
    timestamp: Optional[datetime] = None
    image_path: Optional[str] = None
    processing_time: float = 0.0
    camera_source: str = ""
    
    def __post_init__(self):
        """Set timestamp if not provided."""
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class SystemConfig:
    """Model for system configuration."""
    
    id: Optional[int] = None
    key: str = ""
    value: str = ""
    description: str = ""
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Set timestamp if not provided."""
        if self.updated_at is None:
            self.updated_at = datetime.now()
