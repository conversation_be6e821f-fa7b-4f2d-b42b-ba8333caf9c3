"""
Main GUI window for the number plate detection system.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import threading
import time
from typing import Optional, Dict, Any, List
from datetime import datetime

from ..detection.plate_detector import PlateDetector
from ..detection.ocr_engine import NumberPlateOCR
from ..classification.classifier import VehicleClassifier
from ..video.processor import VideoProcessor
from ..database.db_manager import DatabaseManager
from ..utils.config import ConfigManager
from ..utils.logger import Logger


class MainWindow:
    """Main application window."""
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize main window.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self.logger = Logger.get_logger("MainWindow", config_manager.config)
        
        # Initialize components
        self.db_manager = DatabaseManager(config_manager.get('database.path'))
        self.plate_detector = None
        self.ocr_engine = None
        self.classifier = None
        self.video_processor = None
        
        # GUI components
        self.root = None
        self.video_label = None
        self.status_label = None
        self.detection_listbox = None
        self.stats_frame = None
        
        # Video display
        self.current_frame = None
        self.display_frame = None
        self.video_running = False
        
        # Detection history
        self.detection_history = []
        self.max_history = 100
        
        self._initialize_components()
        self._create_gui()
        
        self.logger.info("Main window initialized")
    
    def _initialize_components(self):
        """Initialize detection and processing components."""
        try:
            # Initialize plate detector
            model_config = self.config_manager.get_section('model')
            self.plate_detector = PlateDetector(
                model_path=model_config.get('yolo_model_path', 'models/yolov8n.pt'),
                confidence_threshold=model_config.get('confidence_threshold', 0.5),
                iou_threshold=model_config.get('iou_threshold', 0.4),
                device=model_config.get('device', 'cpu'),
                logger=self.logger
            )
            
            # Initialize OCR engine
            ocr_config = self.config_manager.get_section('ocr')
            self.ocr_engine = NumberPlateOCR(
                engine=ocr_config.get('engine', 'easyocr'),
                languages=ocr_config.get('languages', ['en']),
                confidence_threshold=ocr_config.get('confidence_threshold', 0.6),
                logger=self.logger
            )
            
            # Initialize classifier
            classification_config = self.config_manager.get_section('classification')
            self.classifier = VehicleClassifier(
                db_manager=self.db_manager,
                match_threshold=classification_config.get('employee_match_threshold', 0.8),
                logger=self.logger
            )
            
            # Initialize video processor
            self.video_processor = VideoProcessor(
                plate_detector=self.plate_detector,
                ocr_engine=self.ocr_engine,
                classifier=self.classifier,
                logger=self.logger
            )
            
            # Configure video processor
            self.video_processor.configure(self.config_manager.config)
            self.video_processor.set_result_callback(self._on_detection_result)
            
        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")
            raise
    
    def _create_gui(self):
        """Create the main GUI."""
        self.root = tk.Tk()
        self.root.title(self.config_manager.get('ui.window_title', 'Number Plate Detection'))
        self.root.geometry("1200x800")
        
        # Create main frames
        self._create_menu()
        self._create_video_frame()
        self._create_control_frame()
        self._create_detection_frame()
        self._create_status_frame()
        
        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _create_menu(self):
        """Create menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open Video File", command=self._open_video_file)
        file_menu.add_command(label="Use Camera", command=self._use_camera)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self._on_closing)
        
        # Database menu
        db_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Database", menu=db_menu)
        db_menu.add_command(label="Add Employee Vehicle", command=self._add_employee_vehicle)
        db_menu.add_command(label="View Employee Vehicles", command=self._view_employee_vehicles)
        db_menu.add_command(label="View Detection History", command=self._view_detection_history)
        
        # Settings menu
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Settings", menu=settings_menu)
        settings_menu.add_command(label="Configuration", command=self._open_settings)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self._show_about)
    
    def _create_video_frame(self):
        """Create video display frame."""
        video_frame = ttk.LabelFrame(self.root, text="Video Feed", padding="5")
        video_frame.grid(row=0, column=0, columnspan=2, sticky="nsew", padx=5, pady=5)
        
        # Video display label
        self.video_label = tk.Label(video_frame, bg="black", width=80, height=30)
        self.video_label.pack(expand=True, fill="both")
        
        # Configure grid weights
        self.root.grid_rowconfigure(0, weight=3)
        self.root.grid_columnconfigure(0, weight=2)
        self.root.grid_columnconfigure(1, weight=1)
    
    def _create_control_frame(self):
        """Create control buttons frame."""
        control_frame = ttk.LabelFrame(self.root, text="Controls", padding="5")
        control_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=5)
        
        # Control buttons
        self.start_button = ttk.Button(control_frame, text="Start Detection", 
                                      command=self._start_detection)
        self.start_button.pack(side="left", padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="Stop Detection", 
                                     command=self._stop_detection, state="disabled")
        self.stop_button.pack(side="left", padx=5)
        
        self.snapshot_button = ttk.Button(control_frame, text="Take Snapshot", 
                                         command=self._take_snapshot)
        self.snapshot_button.pack(side="left", padx=5)
        
        # Source selection
        ttk.Label(control_frame, text="Source:").pack(side="left", padx=(20, 5))
        self.source_var = tk.StringVar(value="Camera")
        source_combo = ttk.Combobox(control_frame, textvariable=self.source_var, 
                                   values=["Camera", "Video File"], state="readonly", width=15)
        source_combo.pack(side="left", padx=5)
    
    def _create_detection_frame(self):
        """Create detection results frame."""
        detection_frame = ttk.LabelFrame(self.root, text="Recent Detections", padding="5")
        detection_frame.grid(row=0, column=2, rowspan=2, sticky="nsew", padx=5, pady=5)
        
        # Detection listbox with scrollbar
        listbox_frame = tk.Frame(detection_frame)
        listbox_frame.pack(fill="both", expand=True)
        
        scrollbar = ttk.Scrollbar(listbox_frame)
        scrollbar.pack(side="right", fill="y")
        
        self.detection_listbox = tk.Listbox(listbox_frame, yscrollcommand=scrollbar.set)
        self.detection_listbox.pack(side="left", fill="both", expand=True)
        scrollbar.config(command=self.detection_listbox.yview)
        
        # Statistics frame
        self.stats_frame = ttk.LabelFrame(detection_frame, text="Statistics", padding="5")
        self.stats_frame.pack(fill="x", pady=(10, 0))
        
        self.stats_labels = {}
        stats_items = ["Total Detections", "Employee Count", "Visitor Count", "FPS"]
        for i, item in enumerate(stats_items):
            label = ttk.Label(self.stats_frame, text=f"{item}: 0")
            label.grid(row=i//2, column=i%2, sticky="w", padx=5, pady=2)
            self.stats_labels[item] = label
        
        self.root.grid_columnconfigure(2, weight=1)
    
    def _create_status_frame(self):
        """Create status bar."""
        status_frame = tk.Frame(self.root)
        status_frame.grid(row=2, column=0, columnspan=3, sticky="ew", padx=5, pady=5)
        
        self.status_label = tk.Label(status_frame, text="Ready", relief="sunken", anchor="w")
        self.status_label.pack(fill="x")
        
        self.root.grid_rowconfigure(2, weight=0)
    
    def _start_detection(self):
        """Start video detection."""
        try:
            # Configure video source
            if self.source_var.get() == "Camera":
                self.video_processor.input_source = 0
            else:
                # Video file should be set via file dialog
                pass
            
            # Start video processing
            if self.video_processor.start_processing():
                self.video_running = True
                self.start_button.config(state="disabled")
                self.stop_button.config(state="normal")
                self.status_label.config(text="Detection started")
                
                # Start video display update
                self._update_video_display()
                
                # Start statistics update
                self._update_statistics()
                
            else:
                messagebox.showerror("Error", "Failed to start video processing")
                
        except Exception as e:
            self.logger.error(f"Error starting detection: {e}")
            messagebox.showerror("Error", f"Failed to start detection: {str(e)}")
    
    def _stop_detection(self):
        """Stop video detection."""
        try:
            self.video_processor.stop_processing()
            self.video_running = False
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.status_label.config(text="Detection stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping detection: {e}")
    
    def _update_video_display(self):
        """Update video display in GUI."""
        if not self.video_running:
            return
        
        try:
            # Get latest result from video processor
            result = self.video_processor.get_latest_result()
            
            if result and 'frame' in result:
                frame = result['frame']
                
                # Draw detections on frame
                if result['detections']:
                    frame = self.video_processor.visualize_detections(frame, result['detections'])
                
                # Convert frame for display
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frame_pil = Image.fromarray(frame_rgb)
                
                # Resize to fit display
                display_size = (640, 480)
                frame_pil = frame_pil.resize(display_size, Image.Resampling.LANCZOS)
                
                # Convert to PhotoImage
                self.display_frame = ImageTk.PhotoImage(frame_pil)
                self.video_label.config(image=self.display_frame)
            
        except Exception as e:
            self.logger.error(f"Error updating video display: {e}")
        
        # Schedule next update
        if self.video_running:
            self.root.after(33, self._update_video_display)  # ~30 FPS
    
    def _update_statistics(self):
        """Update statistics display."""
        if not self.video_running:
            return
        
        try:
            # Get performance stats
            perf_stats = self.video_processor.get_performance_stats()
            
            # Get classification stats
            class_stats = self.classifier.get_statistics()
            
            # Update labels
            self.stats_labels["Total Detections"].config(
                text=f"Total Detections: {class_stats.get('detections_today', 0)}"
            )
            self.stats_labels["Employee Count"].config(
                text=f"Employee Count: {class_stats.get('employee_detections_today', 0)}"
            )
            self.stats_labels["Visitor Count"].config(
                text=f"Visitor Count: {class_stats.get('visitor_detections_today', 0)}"
            )
            self.stats_labels["FPS"].config(
                text=f"FPS: {perf_stats.get('fps', 0):.1f}"
            )
            
        except Exception as e:
            self.logger.error(f"Error updating statistics: {e}")
        
        # Schedule next update
        if self.video_running:
            self.root.after(1000, self._update_statistics)  # Update every second
    
    def _on_detection_result(self, result: Dict[str, Any]):
        """
        Handle detection result from video processor.
        
        Args:
            result: Detection result dictionary
        """
        try:
            for detection in result.get('detections', []):
                # Add to history
                self.detection_history.append(detection)
                if len(self.detection_history) > self.max_history:
                    self.detection_history.pop(0)
                
                # Update detection listbox
                timestamp = detection['timestamp'].strftime("%H:%M:%S")
                plate_text = detection['plate_text']
                classification = detection['classification']['classification']
                
                display_text = f"{timestamp} - {plate_text} ({classification})"
                
                self.detection_listbox.insert(0, display_text)
                
                # Keep only recent items in listbox
                if self.detection_listbox.size() > 50:
                    self.detection_listbox.delete(50, tk.END)
                
        except Exception as e:
            self.logger.error(f"Error handling detection result: {e}")
    
    def _take_snapshot(self):
        """Take a snapshot of current frame."""
        try:
            result = self.video_processor.get_latest_result()
            if result and 'frame' in result:
                frame = result['frame']
                
                # Draw detections
                if result['detections']:
                    frame = self.video_processor.visualize_detections(frame, result['detections'])
                
                # Save snapshot
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"snapshot_{timestamp}.jpg"
                filepath = f"output/{filename}"
                
                cv2.imwrite(filepath, frame)
                messagebox.showinfo("Snapshot", f"Snapshot saved as {filename}")
                
        except Exception as e:
            self.logger.error(f"Error taking snapshot: {e}")
            messagebox.showerror("Error", "Failed to take snapshot")
    
    def _open_video_file(self):
        """Open video file dialog."""
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[("Video files", "*.mp4 *.avi *.mov *.mkv"), ("All files", "*.*")]
        )
        
        if file_path:
            self.video_processor.input_source = file_path
            self.source_var.set("Video File")
            self.status_label.config(text=f"Video file selected: {file_path}")
    
    def _use_camera(self):
        """Switch to camera input."""
        self.video_processor.input_source = 0
        self.source_var.set("Camera")
        self.status_label.config(text="Camera selected")
    
    def _add_employee_vehicle(self):
        """Open dialog to add employee vehicle."""
        # This would open a separate dialog window
        # For now, show a simple input dialog
        messagebox.showinfo("Add Employee Vehicle", 
                           "Employee vehicle management dialog would open here")
    
    def _view_employee_vehicles(self):
        """View employee vehicles."""
        messagebox.showinfo("Employee Vehicles", 
                           "Employee vehicles list would open here")
    
    def _view_detection_history(self):
        """View detection history."""
        messagebox.showinfo("Detection History", 
                           "Detection history window would open here")
    
    def _open_settings(self):
        """Open settings dialog."""
        messagebox.showinfo("Settings", "Settings dialog would open here")
    
    def _show_about(self):
        """Show about dialog."""
        about_text = """Real-Time Number Plate Detection System
        
Version: 1.0.0
Built with YOLOv8 and EasyOCR

Features:
- Real-time plate detection
- Employee/Visitor classification
- Database management
- Video processing"""
        
        messagebox.showinfo("About", about_text)
    
    def _on_closing(self):
        """Handle window closing."""
        if self.video_running:
            self._stop_detection()
        
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """Start the GUI application."""
        self.root.mainloop()
