"""
Real-Time Indian Number Plate Detection and Classification System

A comprehensive Python-based system for real-time detection and classification 
of Indian vehicle number plates using YOLOv8 and OCR technology.
"""

__version__ = "1.0.0"
__author__ = "Your Name"
__email__ = "<EMAIL>"

from .detection import PlateDetector, OCREngine
from .database import DatabaseManager
from .classification import VehicleClassifier
from .video import VideoProcessor
from .utils import ConfigManager, Logger

__all__ = [
    "PlateDetector",
    "OCREngine", 
    "DatabaseManager",
    "VehicleClassifier",
    "VideoProcessor",
    "ConfigManager",
    "Logger"
]
