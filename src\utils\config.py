"""
Configuration management for the number plate detection system.
"""

import yaml
import os
from typing import Any, Dict, Optional
from pathlib import Path


class ConfigManager:
    """Manages application configuration from YAML files."""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize configuration manager.
        
        Args:
            config_path: Path to the configuration file
        """
        self.config_path = config_path
        self._config = {}
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from YAML file."""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as file:
                    self._config = yaml.safe_load(file) or {}
            else:
                print(f"Warning: Config file {self.config_path} not found. Using defaults.")
                self._config = self._get_default_config()
                self.save_config()
        except Exception as e:
            print(f"Error loading config: {e}. Using defaults.")
            self._config = self._get_default_config()
    
    def save_config(self) -> None:
        """Save current configuration to YAML file."""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(self._config, file, default_flow_style=False, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.
        
        Args:
            key: Configuration key (e.g., 'model.confidence_threshold')
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        Set configuration value using dot notation.
        
        Args:
            key: Configuration key (e.g., 'model.confidence_threshold')
            value: Value to set
        """
        keys = key.split('.')
        config = self._config
        
        # Navigate to the parent dictionary
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set the value
        config[keys[-1]] = value
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get entire configuration section.
        
        Args:
            section: Section name
            
        Returns:
            Dictionary containing section configuration
        """
        return self._config.get(section, {})
    
    def update_section(self, section: str, values: Dict[str, Any]) -> None:
        """
        Update entire configuration section.
        
        Args:
            section: Section name
            values: Dictionary with new values
        """
        if section not in self._config:
            self._config[section] = {}
        self._config[section].update(values)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration."""
        return {
            'model': {
                'yolo_model_path': 'models/yolov8n.pt',
                'confidence_threshold': 0.5,
                'iou_threshold': 0.4,
                'device': 'cpu'
            },
            'ocr': {
                'engine': 'easyocr',
                'languages': ['en'],
                'confidence_threshold': 0.6,
                'preprocessing': {
                    'resize_factor': 2.0,
                    'gaussian_blur': True,
                    'morphology': True
                }
            },
            'database': {
                'type': 'sqlite',
                'path': 'data/employee_vehicles.db',
                'backup_enabled': True,
                'backup_interval': 24
            },
            'video': {
                'input_source': 0,
                'output_enabled': True,
                'output_path': 'output/',
                'fps': 30,
                'resolution': {
                    'width': 1280,
                    'height': 720
                }
            },
            'detection': {
                'roi_enabled': False,
                'roi_coordinates': [100, 100, 500, 400],
                'tracking_enabled': True,
                'min_plate_area': 1000,
                'max_plate_area': 50000
            },
            'classification': {
                'employee_match_threshold': 0.8,
                'visitor_log_enabled': True,
                'notification_enabled': False
            },
            'logging': {
                'level': 'INFO',
                'file_enabled': True,
                'file_path': 'logs/system.log',
                'max_file_size': '10MB',
                'backup_count': 5
            },
            'ui': {
                'window_title': 'Real-Time Number Plate Detection',
                'theme': 'dark',
                'show_confidence': True,
                'show_processing_time': True,
                'alert_sound': False
            }
        }
    
    @property
    def config(self) -> Dict[str, Any]:
        """Get the entire configuration dictionary."""
        return self._config.copy()
    
    def validate_config(self) -> bool:
        """
        Validate configuration values.
        
        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            # Check required sections
            required_sections = ['model', 'ocr', 'database', 'video']
            for section in required_sections:
                if section not in self._config:
                    print(f"Missing required section: {section}")
                    return False
            
            # Validate model paths
            model_path = self.get('model.yolo_model_path')
            if model_path and not os.path.exists(model_path):
                print(f"Warning: Model file not found: {model_path}")
            
            # Validate thresholds
            conf_threshold = self.get('model.confidence_threshold', 0.5)
            if not 0 <= conf_threshold <= 1:
                print("Invalid confidence threshold. Must be between 0 and 1.")
                return False
            
            # Validate database path
            db_path = self.get('database.path')
            if db_path:
                db_dir = os.path.dirname(db_path)
                if db_dir and not os.path.exists(db_dir):
                    os.makedirs(db_dir, exist_ok=True)
            
            return True
            
        except Exception as e:
            print(f"Configuration validation error: {e}")
            return False
