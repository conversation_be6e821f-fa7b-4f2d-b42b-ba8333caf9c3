# Core dependencies for Real-Time Number Plate Detection System
ultralytics==8.0.196
opencv-python==4.8.1.78
easyocr==1.7.0
pytesseract==0.3.10
numpy==1.24.3
pandas==2.0.3
Pillow==10.0.0
matplotlib==3.7.2
seaborn==0.12.2

# Database
# sqlite3 is built into Python, no need to install

# GUI and Visualization
# tkinter is built into Python, no need to install
# customtkinter==5.2.0  # Optional for modern UI, commented out for compatibility

# Utilities
python-dotenv==1.0.0
pyyaml==6.0.1
tqdm==4.66.1

# Logging and Configuration
loguru==0.7.0

# Optional: For advanced image processing
scikit-image==0.21.0
imutils==0.5.4

# Development and Testing
pytest==7.4.0
pytest-cov==4.1.0
black==23.7.0
flake8==6.0.0
