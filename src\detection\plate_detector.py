"""
Number plate detection using YOLOv8.
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict, Any
from ultralytics import YOLO
import os
from pathlib import Path

from ..utils.logger import Logger


class PlateDetector:
    """YOLOv8-based number plate detector."""
    
    def __init__(self, model_path: str = "models/yolov8n.pt", 
                 confidence_threshold: float = 0.5,
                 iou_threshold: float = 0.4,
                 device: str = "cpu",
                 logger: Optional[Logger] = None):
        """
        Initialize plate detector.
        
        Args:
            model_path: Path to YOLOv8 model file
            confidence_threshold: Confidence threshold for detections
            iou_threshold: IoU threshold for NMS
            device: Device to run inference on ('cpu' or 'cuda')
            logger: Logger instance
        """
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        self.iou_threshold = iou_threshold
        self.device = device
        self.logger = logger or Logger()
        
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """Load YOLOv8 model."""
        try:
            # Ensure models directory exists
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            
            # Load model (will download if not exists)
            self.model = YOLO(self.model_path)
            
            # Set device
            if self.device == "cuda" and not self._is_cuda_available():
                self.logger.warning("CUDA not available, falling back to CPU")
                self.device = "cpu"
            
            self.logger.info(f"YOLOv8 model loaded successfully from {self.model_path}")
            self.logger.info(f"Using device: {self.device}")
            
        except Exception as e:
            self.logger.error(f"Failed to load YOLOv8 model: {e}")
            raise
    
    def _is_cuda_available(self) -> bool:
        """Check if CUDA is available."""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False
    
    def detect_plates(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        Detect number plates in an image.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of detection dictionaries containing bbox, confidence, etc.
        """
        if self.model is None:
            raise RuntimeError("Model not loaded")
        
        try:
            # Run inference
            results = self.model(
                image,
                conf=self.confidence_threshold,
                iou=self.iou_threshold,
                device=self.device,
                verbose=False
            )
            
            detections = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for i, box in enumerate(boxes):
                        # Extract box coordinates
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())
                        
                        # Filter for license plate class (assuming class 0 or specific class)
                        # Note: You may need to adjust this based on your model's classes
                        if self._is_license_plate_class(class_id):
                            detection = {
                                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                                'confidence': float(confidence),
                                'class_id': class_id,
                                'area': (x2 - x1) * (y2 - y1)
                            }
                            detections.append(detection)
            
            # Filter by area constraints
            detections = self._filter_by_area(detections)
            
            # Sort by confidence
            detections.sort(key=lambda x: x['confidence'], reverse=True)
            
            self.logger.debug(f"Detected {len(detections)} license plates")
            return detections
            
        except Exception as e:
            self.logger.error(f"Error during plate detection: {e}")
            return []
    
    def _is_license_plate_class(self, class_id: int) -> bool:
        """
        Check if class ID corresponds to license plate.
        
        Args:
            class_id: Class ID from YOLO detection
            
        Returns:
            True if it's a license plate class
        """
        # For general YOLO models, you might need to train a custom model
        # or use a pre-trained license plate detection model
        # For now, we'll assume any detection is a potential plate
        return True
    
    def _filter_by_area(self, detections: List[Dict[str, Any]], 
                       min_area: int = 1000, 
                       max_area: int = 50000) -> List[Dict[str, Any]]:
        """
        Filter detections by area constraints.
        
        Args:
            detections: List of detection dictionaries
            min_area: Minimum plate area
            max_area: Maximum plate area
            
        Returns:
            Filtered detections
        """
        filtered = []
        for detection in detections:
            area = detection['area']
            if min_area <= area <= max_area:
                filtered.append(detection)
            else:
                self.logger.debug(f"Filtered out detection with area {area}")
        
        return filtered
    
    def extract_plate_image(self, image: np.ndarray, bbox: List[int], 
                           padding: int = 10) -> np.ndarray:
        """
        Extract plate region from image.
        
        Args:
            image: Source image
            bbox: Bounding box [x1, y1, x2, y2]
            padding: Padding around the plate
            
        Returns:
            Cropped plate image
        """
        x1, y1, x2, y2 = bbox
        h, w = image.shape[:2]
        
        # Add padding
        x1 = max(0, x1 - padding)
        y1 = max(0, y1 - padding)
        x2 = min(w, x2 + padding)
        y2 = min(h, y2 + padding)
        
        # Extract region
        plate_image = image[y1:y2, x1:x2]
        
        return plate_image
    
    def preprocess_plate_image(self, plate_image: np.ndarray) -> np.ndarray:
        """
        Preprocess plate image for better OCR results.
        
        Args:
            plate_image: Raw plate image
            
        Returns:
            Preprocessed plate image
        """
        try:
            # Convert to grayscale if needed
            if len(plate_image.shape) == 3:
                gray = cv2.cvtColor(plate_image, cv2.COLOR_BGR2GRAY)
            else:
                gray = plate_image.copy()
            
            # Resize for better OCR
            height, width = gray.shape
            if height < 50:  # Minimum height for OCR
                scale_factor = 50 / height
                new_width = int(width * scale_factor)
                gray = cv2.resize(gray, (new_width, 50), interpolation=cv2.INTER_CUBIC)
            
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)
            
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # Morphological operations to clean up
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            return cleaned
            
        except Exception as e:
            self.logger.error(f"Error preprocessing plate image: {e}")
            return plate_image
    
    def visualize_detections(self, image: np.ndarray, 
                           detections: List[Dict[str, Any]]) -> np.ndarray:
        """
        Draw detection boxes on image.
        
        Args:
            image: Input image
            detections: List of detections
            
        Returns:
            Image with drawn detections
        """
        result_image = image.copy()
        
        for detection in detections:
            bbox = detection['bbox']
            confidence = detection['confidence']
            
            x1, y1, x2, y2 = bbox
            
            # Draw bounding box
            cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # Draw confidence
            label = f"Plate: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            cv2.rectangle(result_image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), (0, 255, 0), -1)
            cv2.putText(result_image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)
        
        return result_image
    
    def update_model_config(self, confidence_threshold: Optional[float] = None,
                           iou_threshold: Optional[float] = None):
        """
        Update model configuration.
        
        Args:
            confidence_threshold: New confidence threshold
            iou_threshold: New IoU threshold
        """
        if confidence_threshold is not None:
            self.confidence_threshold = confidence_threshold
            self.logger.info(f"Updated confidence threshold to {confidence_threshold}")
        
        if iou_threshold is not None:
            self.iou_threshold = iou_threshold
            self.logger.info(f"Updated IoU threshold to {iou_threshold}")
